"use client";

import React from 'react';
import { Palette } from 'lucide-react';

// Import main editor components
import { Canvas } from '@/components/design/editor/Canvas';
import { ToolPalette } from '@/components/design/editor/ToolPalette';
import { MenuBar } from '@/components/design/editor/MenuBar';
import { StatusBar } from '@/components/design/editor/StatusBar';
import { OptionsBar } from '@/components/design/editor/OptionsBar';
import { Panels } from '@/components/design/editor/Panels';

// Import dialogs
import { ImageGenerationDialog } from '@/components/design/editor/dialogs/ImageGenerationDialog';
import { ImageTransformationDialog } from '@/components/design/editor/dialogs/ImageTransformationDialog';
import { ReviewImageDialog } from '@/components/design/editor/dialogs/ReviewImageDialog';

// Import stores for dialog state management
import { useCanvasStore } from '@/store/design/canvasStore';

export default function DesignStudioPage() {
  // Get dialog states from canvas store
  const { activeAITool, reviewModal } = useCanvasStore();

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Menu Bar */}
      <MenuBar />

      {/* Options Bar */}
      <OptionsBar />

      {/* Main Editor Layout */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Tool Palette */}
        <div className="w-16 bg-sidebar border-r border-border">
          <ToolPalette />
        </div>

        {/* Main Canvas Area */}
        <div className="flex-1 flex flex-col">
          <Canvas />
        </div>

        {/* Right Sidebar - Panels */}
        <div className="w-80 bg-sidebar border-l border-border">
          <Panels />
        </div>
      </div>

      {/* Status Bar */}
      <StatusBar />

      {/* AI Tool Dialogs */}
      {activeAITool?.type === 'ai-image-generation' && (
        <ImageGenerationDialog />
      )}

      {activeAITool?.type === 'ai-image-transformation' && (
        <ImageTransformationDialog />
      )}

      {/* Review Modal */}
      {reviewModal?.isOpen && (
        <ReviewImageDialog />
      )}
    </div>
  );
}
