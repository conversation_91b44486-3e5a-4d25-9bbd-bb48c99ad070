"use client";

import React, { useState } from 'react';
import { Palette } from 'lucide-react';

// Import the main editor components
import { MenuBar } from '@/components/design/editor/MenuBar';
import { ToolPalette } from '@/components/design/editor/ToolPalette';
import { Canvas } from '@/components/design/editor/Canvas';
import { OptionsBar } from '@/components/design/editor/OptionsBar';
import { StatusBar } from '@/components/design/editor/StatusBar';
import { Panels } from '@/components/design/editor/Panels';

// Import dialogs
import { NewDocumentDialog } from '@/components/design/editor/dialogs/NewDocumentDialog';
import { ImageGenerationDialog } from '@/components/design/editor/dialogs/ImageGenerationDialog';
import { ImageTransformationDialog } from '@/components/design/editor/dialogs/ImageTransformationDialog';
import { ReviewImageDialog } from '@/components/design/editor/dialogs/ReviewImageDialog';

// Import adjustment dialogs
import { BrightnessAdjustmentDialog } from '@/components/design/editor/dialogs/BrightnessAdjustmentDialog';
import { ContrastAdjustmentDialog } from '@/components/design/editor/dialogs/ContrastAdjustmentDialog';
import { SaturationAdjustmentDialog } from '@/components/design/editor/dialogs/SaturationAdjustmentDialog';
import { HueAdjustmentDialog } from '@/components/design/editor/dialogs/HueAdjustmentDialog';
import { ExposureAdjustmentDialog } from '@/components/design/editor/dialogs/ExposureAdjustmentDialog';
import { ColorTemperatureAdjustmentDialog } from '@/components/design/editor/dialogs/ColorTemperatureAdjustmentDialog';
import { BlurAdjustmentDialog } from '@/components/design/editor/dialogs/BlurAdjustmentDialog';
import { SharpenAdjustmentDialog } from '@/components/design/editor/dialogs/SharpenAdjustmentDialog';

// Import stores for dialog state management
import { useCanvasStore } from '@/store/design/canvasStore';

export default function DesignStudioPage() {
  const [showNewDocumentDialog, setShowNewDocumentDialog] = useState(false);

  // Get dialog states from canvas store
  const { activeAITool, activeAdjustmentTool, reviewModal } = useCanvasStore();

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Menu Bar */}
      <MenuBar onNewDocument={() => setShowNewDocumentDialog(true)} />

      {/* Options Bar */}
      <OptionsBar />

      {/* Main Editor Layout */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Tool Palette */}
        <div className="w-16 bg-sidebar border-r border-border">
          <ToolPalette />
        </div>

        {/* Main Canvas Area */}
        <div className="flex-1 flex flex-col">
          <Canvas />
        </div>

        {/* Right Sidebar - Panels */}
        <div className="w-80 bg-sidebar border-l border-border">
          <Panels />
        </div>
      </div>

      {/* Status Bar */}
      <StatusBar />

      {/* Dialogs */}
      {showNewDocumentDialog && (
        <NewDocumentDialog
          onClose={() => setShowNewDocumentDialog(false)}
        />
      )}

      {/* AI Tool Dialogs */}
      {activeAITool?.type === 'ai-image-generation' && (
        <ImageGenerationDialog />
      )}

      {activeAITool?.type === 'ai-image-transformation' && (
        <ImageTransformationDialog />
      )}

      {/* Review Modal */}
      {reviewModal?.isOpen && (
        <ReviewImageDialog />
      )}

      {/* Adjustment Dialogs */}
      {activeAdjustmentTool?.toolId === 'brightness' && (
        <BrightnessAdjustmentDialog />
      )}

      {activeAdjustmentTool?.toolId === 'contrast' && (
        <ContrastAdjustmentDialog />
      )}

      {activeAdjustmentTool?.toolId === 'saturation' && (
        <SaturationAdjustmentDialog />
      )}

      {activeAdjustmentTool?.toolId === 'hue' && (
        <HueAdjustmentDialog />
      )}

      {activeAdjustmentTool?.toolId === 'exposure' && (
        <ExposureAdjustmentDialog />
      )}

      {activeAdjustmentTool?.toolId === 'color-temperature' && (
        <ColorTemperatureAdjustmentDialog />
      )}

      {activeAdjustmentTool?.toolId === 'blur' && (
        <BlurAdjustmentDialog />
      )}

      {activeAdjustmentTool?.toolId === 'sharpen' && (
        <SharpenAdjustmentDialog />
      )}
    </div>
  );
}
