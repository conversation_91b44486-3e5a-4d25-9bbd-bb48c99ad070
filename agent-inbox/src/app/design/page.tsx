"use client";

import React from 'react';
import { Palette } from 'lucide-react';

// Import Canvas and ToolPalette for basic drawing functionality
import { Canvas } from '@/components/design/editor/Canvas';
import { ToolPalette } from '@/components/design/editor/ToolPalette';

export default function DesignStudioPage() {
  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-white border-b">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Palette className="w-6 h-6 text-purple-600" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">Visual Design Studio</h1>
            <p className="text-sm text-gray-600">🔧 Testing Canvas Integration</p>
          </div>
        </div>
      </div>

      {/* Main Canvas Area with Tool Palette */}
      <div className="flex-1 flex">
        {/* Left Sidebar - Tool Palette */}
        <div className="w-16 bg-gray-100 border-r border-gray-200">
          <ToolPalette />
        </div>

        {/* Main Canvas Area */}
        <div className="flex-1 bg-white">
          <Canvas />
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-6 py-3 bg-white border-t text-sm text-gray-600">
        <div>Canvas + Tools Integration</div>
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>Canvas + Tools Active</span>
        </div>
      </div>
    </div>
  );
}
